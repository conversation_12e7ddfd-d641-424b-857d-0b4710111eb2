import { ApplicableScope } from '@/constant';
import {
  create,
  index,
  remove,
  update,
} from '@/services/membership-card-types';
import { formatNumber } from '@/utils/format';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space, Switch, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const Type: React.FC = () => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [current, setCurrent] = useState<API.MembershipCardType>();
  const actionRef = useRef<ActionType>();

  // 处理保存
  const handleSave = async (values: API.MembershipCardType) => {
    try {
      let response;
      if (values.id) {
        // 更新
        response = await update(values.id, values);
      } else {
        // 创建
        response = await create(values);
      }

      if (response.errCode) {
        message.error(response.msg || '操作失败');
      } else {
        message.success('操作成功');
        actionRef.current?.reload();
        setModalVisible(false);
      }
    } catch (error) {
      console.error('保存权益卡类型失败', error);
      message.error('操作失败，请重试');
    }
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      const response = await remove(id);
      if (response.errCode) {
        message.error(response.msg || '删除失败');
      } else {
        message.success('删除成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('删除权益卡类型失败', error);
      message.error('删除失败，请重试');
    }
  };

  // 处理状态切换
  const handleStatusChange = async (
    checked: boolean,
    record: API.MembershipCardType,
  ) => {
    try {
      const response = await update(record.id, { isEnabled: checked });
      if (response.errCode) {
        message.error(response.msg || '状态更新失败');
      } else {
        message.success('状态更新成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('更新权益卡类型状态失败', error);
      message.error('状态更新失败，请重试');
    }
  };

  // 表格列定义
  const columns: ProColumns<API.MembershipCardType>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '权益卡名称',
      dataIndex: 'name',
      ellipsis: true,
      width: 150,
    },
    {
      title: '卡类型',
      dataIndex: 'type',
      hideInSearch: true,
      width: 100,
      valueEnum: {
        discount: { text: '折扣卡', status: 'Processing' },
        times: { text: '次卡', status: 'Success' },
      },
    },
    {
      title: '售价',
      dataIndex: 'price',
      hideInSearch: true,
      width: 100,
      render: (_, record) => `¥${record.price}`,
    },
    {
      title: '有效期',
      dataIndex: 'validDays',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.validDays ? `${record.validDays}天` : '无限期',
    },
    {
      title: '折扣率',
      dataIndex: 'discountRate',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.discountRate
          ? `${formatNumber(record.discountRate * 10, 1)}折`
          : '-',
    },
    {
      title: '可用次数',
      dataIndex: 'usageLimit',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.usageLimit && record.usageLimit >= 0
          ? `${record.usageLimit}次`
          : '不限',
    },
    {
      title: '适用范围',
      dataIndex: 'applicableScope',
      hideInSearch: true,
      width: 150,
      ellipsis: true,
      render: (_scope, record) => {
        // 根据适用范围类型显示不同的标签
        switch (record.applicableScope) {
          case ApplicableScope.不限:
            return <Tag color="green">全部商品和服务</Tag>;
          case ApplicableScope.所有商品:
            return <Tag color="blue">所有商品</Tag>;
          case ApplicableScope.所有服务:
            return <Tag color="blue">所有服务</Tag>;
          case ApplicableScope.指定服务类别:
            return <Tag color="orange">指定服务类别</Tag>;
          case ApplicableScope.指定服务品牌:
            return <Tag color="orange">指定服务品牌</Tag>;
          case ApplicableScope.指定服务:
            return <Tag color="purple">指定服务</Tag>;
          case ApplicableScope.指定商品类别: {
            return <Tag color="orange">指定商品类别</Tag>;
          }
          case 'product':
            return <Tag color="blue">指定商品</Tag>;
          default:
            return <Tag color="purple">未指定</Tag>;
        }
      },
    },
    {
      title: '使用说明',
      dataIndex: 'description',
      hideInSearch: true,
      width: 150,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'isEnabled',
      width: 100,
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Error' },
      },
      render: (_, record) => (
        <Switch
          checked={record.isEnabled}
          onChange={(checked) => handleStatusChange(checked, record)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此权益卡类型吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.MembershipCardType>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
        request={async (params) => {
          const response = await index({
            ...params,
          });

          if (response.errCode) {
            message.error(response.msg || '获取权益卡类型列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增权益卡
          </Button>,
        ]}
      />

      <EditModal
        open={modalVisible}
        info={current}
        onSave={handleSave}
        onClose={() => {
          setCurrent(undefined);
          setModalVisible(false);
        }}
      />
    </>
  );
};

export default Type;
