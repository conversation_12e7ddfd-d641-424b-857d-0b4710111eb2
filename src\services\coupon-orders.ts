/*
 * @Description: 代金券订单
 */
import { request } from '@umijs/max';

/** 查询列表  GET /coupon-orders/all */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.CouponOrder[] }>>(
    '/coupon-orders/all',
    {
      method: 'GET',
      params,
    },
  );
}

/** 按ID查询  GET /coupon-orders/:id */
export async function show(id: number) {
  return request<API.ResType<API.CouponOrder>>(`/coupon-orders/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /coupon-orders/:id */
export async function update(id: number, body: Partial<API.CouponOrder>) {
  return request<API.ResType<unknown>>(`/coupon-orders/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /coupon-orders/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/coupon-orders/${id}`, {
    method: 'DELETE',
  });
}

/** 支付订单  POST /coupon-orders/:sn/pay */
export async function pay(sn: string, customerId: number) {
  return request<API.ResType<unknown>>(`/coupon-orders/${sn}/pay`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      customerId,
    },
  });
}

/** 取消订单  PUT /coupon-orders/:sn/cancel */
export async function cancel(sn: string, customerId: number) {
  return request<API.ResType<unknown>>(`/coupon-orders/${sn}/cancel`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      customerId,
    },
  });
}

/** 根据用户ID获取订单列表  GET /coupon-orders */
export async function getOrdersByCustomer(
  customerId: number,
  params: Record<string, any>,
) {
  return request<API.ResType<{ total?: number; list?: API.CouponOrder[] }>>(
    `/coupon-orders`,
    {
      method: 'GET',
      params: {
        customerId,
        ...params,
      },
    },
  );
}

/** 根据代金券ID获取订单列表  GET /coupon-orders */
export async function getOrdersByCoupon(
  couponId: number,
  params: Record<string, any>,
) {
  return request<API.ResType<{ total?: number; list?: API.CouponOrder[] }>>(
    `/coupon-orders`,
    {
      method: 'GET',
      params: {
        couponId,
        ...params,
      },
    },
  );
}

// 管理员专用接口（代金券订单）

/** 管理员申请退款代金券订单 POST /admin/coupon-orders/:id/refund */
export async function adminRefund(
  id: number,
  data: { operatorId: number; reason?: string },
) {
  return request<API.ResType<boolean>>(`/admin/coupon-orders/${id}/refund`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/** 管理员删除代金券订单 DELETE /admin/coupon-orders/:id */
export async function adminDelete(
  id: number,
  data: { operatorId: number; reason?: string },
) {
  return request<API.ResType<any>>(`/admin/coupon-orders/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/** 管理端查询微信支付状态 GET /admin/wepay/transactions/sn/:sn */
export async function queryWepayStatus(sn: string) {
  return request<API.ResType<any>>(`/admin/wepay/transactions/sn/${sn}`, {
    method: 'GET',
  });
}
