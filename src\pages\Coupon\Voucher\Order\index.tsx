import { CouponOrderStatus } from '@/constant';
import {
  adminDelete,
  adminRefund,
  cancel,
  index,
  pay,
  queryWepayStatus,
} from '@/services/coupon-orders';
import { DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Button, Dropdown, message, Modal, Space, Tag } from 'antd';
import moment from 'moment';
import React, { useRef, useState } from 'react';
import AdminActionModal from '../../../Appointment/AdminActionModal';
import OrderDetailDrawer from './components/OrderDetailDrawer';

/**
 * 代金券订单管理组件
 */
const Order: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [currentOrder, setCurrentOrder] = useState<API.CouponOrder | undefined>(
    undefined,
  );

  // 管理员操作弹窗状态
  const [adminActionVisible, setAdminActionVisible] = useState<boolean>(false);
  const [adminActionType, setAdminActionType] = useState<'refund' | 'delete'>(
    'refund',
  );
  const [adminActionLoading, setAdminActionLoading] = useState<boolean>(false);

  // 处理查看详情
  const handleViewDetail = (record: API.CouponOrder) => {
    setCurrentOrder(record);
    setDetailVisible(true);
  };

  // 处理支付订单
  const handlePay = async (record: API.CouponOrder) => {
    if (!record.sn || !record.customerId) {
      message.error('订单信息不完整，无法支付');
      return;
    }

    try {
      const response = await pay(record.sn, record.customerId);
      if (response.errCode) {
        message.error(response.msg || '支付失败');
      } else {
        message.success('支付成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('支付失败', error);
      message.error('支付失败，请重试');
    }
  };

  // 处理取消订单
  const handleCancel = async (record: API.CouponOrder) => {
    if (!record.sn || !record.customerId) {
      message.error('订单信息不完整，无法取消');
      return;
    }

    try {
      const response = await cancel(record.sn, record.customerId);
      if (response.errCode) {
        message.error(response.msg || '取消失败');
      } else {
        message.success('取消成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('取消失败', error);
      message.error('取消失败，请重试');
    }
  };

  // 处理查询微信支付状态
  const handleQueryWepayStatus = async (sn: string) => {
    try {
      const response = await queryWepayStatus(sn);
      if (response.errCode) {
        message.error(response.msg || '查询支付状态失败');
      } else {
        message.success('支付状态已同步');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('查询支付状态失败', error);
      message.error('查询支付状态失败，请重试');
    }
  };

  /** 管理员操作 - 打开确认弹窗 */
  const handleAdminAction = (
    record: API.CouponOrder,
    actionType: 'refund' | 'delete',
  ) => {
    setCurrentOrder(record);
    setAdminActionType(actionType);
    setAdminActionVisible(true);
  };

  /** 管理员操作 - 确认执行 */
  const handleAdminActionConfirm = async (reason?: string) => {
    if (!currentOrder || !initialState?.id) {
      message.error('操作失败：缺少必要信息');
      return;
    }

    setAdminActionLoading(true);
    try {
      const operatorId = initialState.id;
      let response;

      switch (adminActionType) {
        case 'refund':
          response = await adminRefund(currentOrder.id, { operatorId, reason });
          break;
        case 'delete':
          response = await adminDelete(currentOrder.id, { operatorId, reason });
          break;
        default:
          message.error('未知操作类型');
          return;
      }

      if (response.errCode) {
        message.error(response.msg);
      } else {
        const actionText =
          adminActionType === 'refund' ? '退款申请已提交' : '删除成功';
        message.success(actionText);
        actionRef?.current?.reload();
        setAdminActionVisible(false);
      }
    } catch (error) {
      console.error('管理员操作失败:', error);
      message.error('操作失败，请重试');
    } finally {
      setAdminActionLoading(false);
    }
  };

  /** 生成财务操作下拉菜单 */
  const getFinanceMenuItems = (record: API.CouponOrder): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 支付
    if (record.status === CouponOrderStatus.待付款) {
      items.push({
        key: 'pay',
        label: '支付',
        onClick: () => handlePay(record),
      });
    }

    // 取消
    if (record.status === CouponOrderStatus.待付款) {
      items.push({
        key: 'cancel',
        label: <span style={{ color: '#ff4d4f' }}>取消</span>,
        onClick: () => {
          Modal.confirm({
            title: '确定要取消此订单吗？',
            icon: <ExclamationCircleOutlined />,
            onOk() {
              handleCancel(record);
            },
          });
        },
      });
    }

    // 查询微信支付状态
    if (
      [CouponOrderStatus.待付款, CouponOrderStatus.已付款].includes(
        record.status,
      )
    ) {
      items.push({
        key: 'query-wepay-status',
        label: '查询支付状态',
        onClick: () => {
          if (record.sn) {
            handleQueryWepayStatus(record.sn);
          } else {
            message.error('订单编号不存在，无法查询支付状态');
          }
        },
      });
    }

    return items;
  };

  /** 生成管理员操作下拉菜单 */
  const getAdminMenuItems = (record: API.CouponOrder): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 管理员退款
    if (record.status !== CouponOrderStatus.已退款) {
      items.push({
        key: 'admin-refund',
        label: '管理员退款',
        onClick: () => handleAdminAction(record, 'refund'),
        style: { color: '#ff4d4f' },
      });
    }

    // 管理员删除
    items.push({
      key: 'admin-delete',
      label: '管理员删除',
      onClick: () => handleAdminAction(record, 'delete'),
      style: { color: '#cf1322' },
    });

    return items;
  };

  // 表格列定义
  const columns: ProColumns<API.CouponOrder>[] = [
    {
      title: '订单ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '订单编号',
      dataIndex: 'sn',
      width: 180,
      copyable: true,
    },
    {
      title: '用户信息',
      dataIndex: 'customerInfo',
      width: 140,
      render: (_, record) => {
        const nickname =
          record.customer?.nickname || `用户${record.customerId}`;
        const phone = record.customer?.phone;
        return (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>{nickname}</div>
            <div
              style={{ fontSize: '12px', color: '#666', cursor: 'pointer' }}
              onClick={() => navigator.clipboard?.writeText(phone || '')}
            >
              {phone || '-'}
            </div>
          </div>
        );
      },
    },
    {
      title: '代金券信息',
      dataIndex: 'couponInfo',
      width: 160,
      render: (_, record) => {
        const couponName = record.coupon?.name;
        const couponAmount = record.coupon?.amount;

        return (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>{couponName}</div>
            <div style={{ fontSize: '12px', color: '#1890ff' }}>
              <Tag color="processing">面额¥{couponAmount}</Tag>
            </div>
          </div>
        );
      },
      search: false,
    },
    {
      title: '订单金额',
      dataIndex: 'amount',
      width: 100,
      search: false,
      render: (_, record) => `¥${record.amount}`,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        [CouponOrderStatus.待付款]: {
          text: '待付款',
          status: 'Warning',
        },
        [CouponOrderStatus.已付款]: {
          text: '已支付',
          status: 'Success',
        },
        [CouponOrderStatus.已取消]: {
          text: '已取消',
          status: 'Default',
        },
        [CouponOrderStatus.已退款]: { text: '已退款', status: 'Error' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      valueType: 'dateTime',
      sorter: true,
      search: false,
      render: (_, record) =>
        record.createdAt
          ? moment(record.createdAt).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '支付时间',
      dataIndex: 'payTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.payTime
          ? moment(record.payTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 160,
      fixed: 'right',
      render: (_, record) => {
        const financeMenuItems = getFinanceMenuItems(record);
        const adminMenuItems = getAdminMenuItems(record);

        return (
          <Space>
            {/* 基础操作 - 始终显示 */}
            <Button type="link" onClick={() => handleViewDetail(record)}>
              详情
            </Button>

            {/* 更多操作下拉菜单 - 分组显示 */}
            <Dropdown
              menu={{
                items: [
                  // 财务操作组
                  ...(financeMenuItems && financeMenuItems.length > 0
                    ? [
                        {
                          type: 'group',
                          label: '财务操作',
                          children: financeMenuItems,
                        },
                      ]
                    : []),

                  // 管理员操作组
                  ...(adminMenuItems && adminMenuItems.length > 0
                    ? [
                        ...(financeMenuItems && financeMenuItems.length > 0
                          ? [{ type: 'divider' }]
                          : []),
                        {
                          type: 'group',
                          label: '管理员操作',
                          children: adminMenuItems,
                        },
                      ]
                    : []),
                ].filter(Boolean) as MenuProps['items'],
              }}
              trigger={['click']}
            >
              <Button type="link">
                更多 <DownOutlined />
              </Button>
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable<API.CouponOrder>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1500 }}
        request={async (params) => {
          const response = await index({
            ...params,
          });

          if (response.errCode) {
            message.error(response.msg || '获取代金券订单列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />

      {/* 订单详情抽屉 */}
      <OrderDetailDrawer
        visible={detailVisible}
        order={currentOrder}
        onClose={() => {
          setDetailVisible(false);
          setCurrentOrder(undefined);
        }}
      />

      {/* 管理员操作确认弹窗 */}
      <AdminActionModal
        open={adminActionVisible}
        loading={adminActionLoading}
        title={`管理员${
          adminActionType === 'refund' ? '申请退款' : '删除'
        }代金券订单`}
        content={`确定要${
          adminActionType === 'refund' ? '申请退款' : '删除'
        }代金券订单 ${currentOrder?.sn} 吗？`}
        actionType={adminActionType as 'cancel' | 'refund' | 'delete'}
        onCancel={() => {
          setAdminActionVisible(false);
          setAdminActionLoading(false);
        }}
        onConfirm={handleAdminActionConfirm}
      />
    </>
  );
};

export default Order;
