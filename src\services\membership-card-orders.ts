/*
 * @Description: 权益卡订单
 */
import { request } from '@umijs/max';

/** 查询列表  GET /membership-card-orders */
export async function index(params: Record<string, any>) {
  return request<
    API.ResType<{ total?: number; list?: API.MembershipCardOrder[] }>
  >('/membership-card-orders/all', {
    method: 'GET',
    params,
  });
}

/** 按ID查询  GET /membership-card-orders/:id */
export async function show(id: number) {
  return request<API.ResType<API.MembershipCardOrder>>(
    `/membership-card-orders/${id}`,
    {
      method: 'GET',
    },
  );
}

/** 修改  PUT /membership-card-orders/:id */
export async function update(
  id: number,
  body: Partial<API.MembershipCardOrder>,
) {
  return request<API.ResType<unknown>>(`/membership-card-orders/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /membership-card-orders/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/membership-card-orders/${id}`, {
    method: 'DELETE',
  });
}

/** 支付订单  POST /membership-card-orders/:sn/pay */
export async function pay(sn: string, customerId: number) {
  return request<API.ResType<unknown>>(`/membership-card-orders/${sn}/pay`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      customerId,
    },
  });
}

/** 取消订单  PUT /membership-card-orders/:sn/cancel */
export async function cancel(sn: string, customerId: number) {
  return request<API.ResType<unknown>>(`/membership-card-orders/${sn}/cancel`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      customerId,
    },
  });
}

/** 根据用户ID获取订单列表  GET /membership-card-orders/customer/:customerId */
export async function getOrdersByCustomer(
  customerId: number,
  params: Record<string, any>,
) {
  return request<
    API.ResType<{ total?: number; list?: API.MembershipCardOrder[] }>
  >(`/membership-card-orders`, {
    method: 'GET',
    params: {
      customerId,
      ...params,
    },
  });
}

/** 根据权益卡类型ID获取订单列表  GET /membership-card-orders/card-type/:cardTypeId */
export async function getOrdersByCardType(
  cardTypeId: number,
  params: Record<string, any>,
) {
  return request<
    API.ResType<{ total?: number; list?: API.MembershipCardOrder[] }>
  >(`/membership-card-orders/card-type/${cardTypeId}`, {
    method: 'GET',
    params,
  });
}

// 管理员专用接口（权益卡订单）

/** 管理员申请退款权益卡订单 POST /admin/membership-card-orders/:id/refund */
export async function adminRefund(
  id: number,
  data: { operatorId: number; reason?: string },
) {
  return request<API.ResType<boolean>>(
    `/admin/membership-card-orders/${id}/refund`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
    },
  );
}

/** 管理员删除权益卡订单 DELETE /admin/membership-card-orders/:id */
export async function adminDelete(
  id: number,
  data: { operatorId: number; reason?: string },
) {
  return request<API.ResType<any>>(`/admin/membership-card-orders/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/** 管理端查询微信支付状态 GET /admin/wepay/transactions/sn/:sn */
export async function queryWepayStatus(sn: string) {
  return request<API.ResType<any>>(`/admin/wepay/transactions/sn/${sn}`, {
    method: 'GET',
  });
}
