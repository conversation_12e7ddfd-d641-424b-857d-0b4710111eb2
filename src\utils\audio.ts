/**
 * 音频播放工具
 */

// 创建音频实例
let orderNotificationAudio: HTMLAudioElement | null = null;

/**
 * 初始化音频
 */
export function initAudio() {
  if (!orderNotificationAudio) {
    orderNotificationAudio = new Audio();

    // 使用简单的data URL创建提示音
    // 这是一个简单的440Hz正弦波音频
    const sampleRate = 8000;
    const duration = 0.3; // 300ms
    const samples = sampleRate * duration;
    const frequency = 800; // 800Hz

    // 创建WAV文件头
    const header = new ArrayBuffer(44);
    const view = new DataView(header);

    // WAV文件头
    view.setUint32(0, 0x52494646, false); // "RIFF"
    view.setUint32(8, 0x57415645, false); // "WAVE"
    view.setUint32(12, 0x666d7420, false); // "fmt "
    view.setUint32(16, 16, true); // fmt chunk size
    view.setUint16(20, 1, true); // PCM format
    view.setUint16(22, 1, true); // mono
    view.setUint32(24, sampleRate, true); // sample rate
    view.setUint32(28, sampleRate * 2, true); // byte rate
    view.setUint16(32, 2, true); // block align
    view.setUint16(34, 16, true); // bits per sample
    view.setUint32(36, 0x64617461, false); // "data"
    view.setUint32(40, samples * 2, true); // data size

    // 创建音频数据
    const audioData = new Int16Array(samples);
    for (let i = 0; i < samples; i++) {
      const t = i / sampleRate;
      const amplitude = Math.exp(-t * 3) * 0.3; // 衰减
      audioData[i] = Math.sin(2 * Math.PI * frequency * t) * amplitude * 32767;
    }

    // 合并头部和数据
    const wavFile = new Uint8Array(44 + samples * 2);
    wavFile.set(new Uint8Array(header), 0);
    wavFile.set(new Uint8Array(audioData.buffer), 44);

    // 创建blob URL
    const blob = new Blob([wavFile], { type: 'audio/wav' });
    const audioUrl = URL.createObjectURL(blob);
    orderNotificationAudio.src = audioUrl;

    // 设置文件大小
    view.setUint32(4, wavFile.length - 8, true);

    // 预加载音频
    orderNotificationAudio.load();
  }
}

/**
 * 播放订单提示音
 */
export function playOrderNotification() {
  try {
    // 使用更简单的方式：直接使用Web Audio API播放提示音
    const audioContext = new (window.AudioContext ||
      (window as any).webkitAudioContext)();

    // 创建振荡器
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    // 连接节点
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // 设置音频参数
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // 800Hz
    oscillator.type = 'sine';

    // 设置音量包络（渐强渐弱）
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.05);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      audioContext.currentTime + 0.3,
    );

    // 播放音频
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  } catch (error) {
    console.warn('音频播放失败:', error);
    // 降级方案：使用系统提示音
    try {
      // 创建一个简单的beep音效
      const audio = new Audio(
        'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT',
      );
      audio.volume = 0.1;
      audio.play().catch(() => {
        // 静默失败
      });
    } catch {
      // 完全静默失败
    }
  }
}
