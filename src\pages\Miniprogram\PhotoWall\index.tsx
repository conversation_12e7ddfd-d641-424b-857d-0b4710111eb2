import {
  getStatistics,
  index,
  remove,
  setPriority,
  toggleEnabled,
} from '@/services/photo-walls';
import { EyeOutlined, PictureOutlined, PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  <PERSON>ton,
  Card,
  Col,
  Image,
  InputNumber,
  message,
  Popconfirm,
  Row,
  Space,
  Statistic,
  Switch,
  Typography,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import CreateFromServiceModal from './CreateFromServiceModal';
import EditModal from './EditModal';

const { Text } = Typography;

const PhotoWallManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [current, setCurrent] = useState<API.PhotoWall | undefined>(undefined);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [createFromServiceVisible, setCreateFromServiceVisible] =
    useState(false);
  const [statistics, setStatistics] = useState<API.PhotoWallStatistics | null>(
    null,
  );

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const { errCode, data } = await getStatistics();
      if (!errCode && data) {
        setStatistics(data);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, []);

  // 处理保存
  const handleSave = async (values: any) => {
    console.log('保存', values);
    try {
      // 这里会在EditModal中处理具体的保存逻辑
      message.success('保存成功');
      setEditModalVisible(false);
      setCurrent(undefined);
      actionRef.current?.reload();
      fetchStatistics();
    } catch (error) {
      message.error('保存失败');
    }
  };

  // 处理删除
  const handleDelete = async (record: API.PhotoWall) => {
    try {
      const { errCode, msg } = await remove(record.id);
      if (errCode) {
        message.error(msg || '删除失败');
      } else {
        message.success('删除成功');
        actionRef.current?.reload();
        fetchStatistics();
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理启用/禁用
  const handleToggleEnabled = async (
    record: API.PhotoWall,
    enabled: boolean,
  ) => {
    try {
      const { errCode, msg } = await toggleEnabled(record.id, enabled);
      if (errCode) {
        message.error(msg || '操作失败');
      } else {
        message.success(enabled ? '启用成功' : '禁用成功');
        actionRef.current?.reload();
        fetchStatistics();
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 处理优先级设置
  const handleSetPriority = async (record: API.PhotoWall, priority: number) => {
    try {
      const { errCode, msg } = await setPriority(record.id, priority);
      if (errCode) {
        message.error(msg || '设置失败');
      } else {
        message.success('优先级设置成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      message.error('设置失败');
    }
  };

  // 渲染照片预览
  const renderPhotoPreview = (beforePhoto: string, afterPhoto: string) => (
    <Space>
      <div style={{ textAlign: 'center' }}>
        <Image
          src={beforePhoto}
          alt="服务前"
          width={60}
          height={60}
          style={{ objectFit: 'cover', borderRadius: '4px' }}
          preview={{
            mask: <EyeOutlined />,
          }}
        />
        <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
          服务前
        </div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <Image
          src={afterPhoto}
          alt="服务后"
          width={60}
          height={60}
          style={{ objectFit: 'cover', borderRadius: '4px' }}
          preview={{
            mask: <EyeOutlined />,
          }}
        />
        <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
          服务后
        </div>
      </div>
    </Space>
  );

  const columns: ProColumns<API.PhotoWall>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '照片预览',
      key: 'photos',
      width: 150,
      hideInSearch: true,
      render: (_, record) =>
        renderPhotoPreview(record.beforePhoto, record.afterPhoto),
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 150,
      ellipsis: true,
    },
    {
      title: '服务类型',
      dataIndex: 'serviceTypeName',
      key: 'serviceTypeName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '宠物信息',
      key: 'petInfo',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (
        <div>
          <div>{record.petName}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.petType}
          </Text>
        </div>
      ),
    },
    {
      title: '客户',
      dataIndex: ['customer', 'nickname'],
      key: 'customerNickname',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '服务人员',
      dataIndex: ['employee', 'name'],
      key: 'employeeName',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (
        <InputNumber
          min={0}
          max={999}
          defaultValue={record.priority}
          onBlur={(e) => {
            const value = parseInt(e.target.value) || 0;
            if (value !== record.priority) {
              handleSetPriority(record, value);
            }
          }}
          onPressEnter={(e) => {
            const value = parseInt((e.target as HTMLInputElement).value) || 0;
            if (value !== record.priority) {
              handleSetPriority(record, value);
            }
          }}
          style={{ width: '80px' }}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'isEnabled',
      key: 'isEnabled',
      width: 100,
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Default' },
      },
      render: (_, record) => (
        <Switch
          checked={record.isEnabled}
          onChange={(checked) => handleToggleEnabled(record, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '数据统计',
      key: 'statistics',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            <PictureOutlined style={{ color: '#1890ff' }} /> {record.viewCount}
          </div>
          <div style={{ fontSize: '12px', marginTop: '2px' }}>
            ❤️ {record.likeCount}
          </div>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurrent(record);
              setEditModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这条照片墙记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      {/* 统计信息卡片 */}
      {statistics && (
        <Row gutter={16} style={{ marginBottom: 16, display: 'none' }}>
          <Col span={6}>
            <Card>
              <Statistic title="总照片数" value={statistics.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="启用照片"
                value={statistics.enabled}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="总浏览数" value={statistics.totalViews} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总点赞数"
                value={statistics.totalLikes}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 照片墙列表 */}
      <ProTable<API.PhotoWall>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1400 }}
        toolBarRender={() => [
          <Button
            key="createFromService"
            icon={<PictureOutlined />}
            onClick={() => setCreateFromServiceVisible(true)}
          >
            从服务照片创建
          </Button>,
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setCurrent(undefined);
              setEditModalVisible(true);
            }}
          >
            手动新增
          </Button>,
        ]}
      />

      {/* 编辑模态框 */}
      <EditModal
        visible={editModalVisible}
        current={current}
        onClose={() => {
          setEditModalVisible(false);
          setCurrent(undefined);
        }}
        onSave={handleSave}
      />

      {/* 从服务照片创建模态框 */}
      <CreateFromServiceModal
        visible={createFromServiceVisible}
        onClose={() => setCreateFromServiceVisible(false)}
        onSuccess={() => {
          setCreateFromServiceVisible(false);
          actionRef.current?.reload();
          fetchStatistics();
        }}
      />
    </>
  );
};

export default PhotoWallManagement;
