import { getPendingCount } from '@/services/order';
import { playOrderNotification } from '@/utils/audio';
import { BellOutlined } from '@ant-design/icons';
import { Badge, Popover, Typography } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

const { Text } = Typography;

interface PendingNotificationProps {
  /** 是否启用自动刷新 */
  enabled?: boolean;
  /** 刷新间隔（毫秒），默认5秒 */
  interval?: number;
  /** 样式 */
  style?: React.CSSProperties;
  /** 类名 */
  className?: string;
}

interface PendingData {
  count: number; // 待办总数
}

/**
 * 待办通知组件
 * 用于显示待办数量并在数量增加时播放提示音
 */
const PendingNotification: React.FC<PendingNotificationProps> = ({
  enabled = true,
  interval = 5000,
  style,
  className,
}) => {
  const [pendingData, setPendingData] = useState<PendingData>({
    count: 0,
  });
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);

  // 使用ref保存上一次的待办数量，用于比较是否增加
  const prevCountRef = useRef<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取待办数量
  const fetchPendingCount = async () => {
    if (!enabled) return;

    setLoading(true);
    try {
      const { errCode, data } = await getPendingCount();
      if (!errCode && data) {
        const newCount = data.count;
        const prevCount = prevCountRef.current;

        // 如果数量增加了，播放提示音
        if (newCount > prevCount && prevCount > 0) {
          playOrderNotification();
        }

        setPendingData(data);
        prevCountRef.current = newCount;
      }
    } catch (error) {
      console.error('获取待办数量失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 启动定时器
  const startTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    if (enabled) {
      // 立即执行一次
      fetchPendingCount();

      // 设置定时器
      timerRef.current = setInterval(fetchPendingCount, interval);
    }
  };

  // 停止定时器
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // 组件挂载时启动定时器
  useEffect(() => {
    startTimer();

    // 组件卸载时清理定时器
    return () => {
      stopTimer();
    };
  }, [enabled, interval]);

  // 当enabled或interval变化时重新启动定时器
  useEffect(() => {
    if (enabled) {
      startTimer();
    } else {
      stopTimer();
    }
  }, [enabled, interval]);

  // 气泡内容
  const popoverContent = (
    <div style={{ minWidth: 180 }}>
      <div style={{ marginBottom: 8 }}>
        <Text>当前待办：</Text>
        <Text strong style={{ color: '#ff4d4f', fontSize: '16px' }}>
          {pendingData.count}
        </Text>
        <Text> 个</Text>
      </div>
      <div style={{ fontSize: '12px', color: '#666' }}>
        包含待接单、待服务、退款中订单
      </div>
      <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
        每5秒自动刷新
      </div>
    </div>
  );

  return (
    <Popover
      content={popoverContent}
      title="待办事项"
      trigger="hover"
      open={visible}
      onOpenChange={setVisible}
      placement="bottomRight"
    >
      <div
        style={{
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          padding: '4px 8px',
          borderRadius: '4px',
          transition: 'background-color 0.2s',
          ...style,
        }}
        className={className}
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
      >
        <Badge
          count={pendingData.count}
          size="small"
          style={{
            backgroundColor: pendingData.count > 0 ? '#ff4d4f' : '#d9d9d9',
          }}
        >
          <BellOutlined
            className={loading ? styles['loading-icon'] : ''}
            style={{
              fontSize: '16px',
              color: pendingData.count > 0 ? '#ff4d4f' : '#666',
            }}
          />
        </Badge>
        {pendingData.count > 0 && (
          <Text
            style={{
              marginLeft: 8,
              fontSize: '12px',
              color: '#ff4d4f',
              fontWeight: 'bold',
            }}
          >
            有新待办
          </Text>
        )}
      </div>
    </Popover>
  );
};

export default PendingNotification;
