import ProFormImg from '@/components/ProFormItem/ProFormImg';
import { DictionarieState } from '@/models/dictionarie';
import {
  ModalForm,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Col, Row } from 'antd';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.AdditionalServiceAttrs;
  onSave: (info: API.AdditionalServiceAttrs) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  dictionarie,
}) => {
  return (
    <ModalForm<API.AdditionalServiceAttrs>
      title={info ? '编辑增项服务' : '注册增项服务'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info}
    >
      <ProFormText name="id" label="ID" hidden />
      <Row gutter={[16, 16]} style={{ width: '100%' }}>
        <Col span={16}>
          <ProFormText
            name="name"
            label="服务名称"
            rules={[{ required: true, message: '请输入服务名称！' }]}
          />
          <ProFormSelect
            name="type"
            label="服务类型"
            rules={[{ required: true, message: '请选择服务类型！' }]}
            options={dictionarie.list
              .filter((d) => d.type === '增项服务类型')
              .map((item) => ({
                label: item.name,
                value: item.code,
              }))}
          />
        </Col>
        <Col span={8}>
          <ProFormImg
            name="logo"
            label="服务logo"
            rules={[{ required: true, message: '请上传服务logo！' }]}
          />
        </Col>
      </Row>

      <ProFormDigit
        name="price"
        label="服务价格"
        rules={[{ required: true, message: '请输入服务价格！' }]}
        colProps={{ span: 12 }}
        min={0}
        fieldProps={{ precision: 2 }}
      />
      <ProFormDigit
        name="duration"
        label="服务时长"
        colProps={{ span: 12 }}
        min={1}
        suffix="分钟"
      />
      <ProFormSwitch
        name="needDurationTracking"
        label="统计时长"
        colProps={{ span: 12 }}
        tooltip="开启后，该增项服务将参与时长统计和计时功能"
      />
      <ProFormTextArea
        name="description"
        label="服务说明"
        colProps={{ span: 24 }}
      />
    </ModalForm>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(EditModal);
