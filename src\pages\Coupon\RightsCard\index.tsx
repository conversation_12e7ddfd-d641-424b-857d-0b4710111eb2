import { Tabs } from 'antd';
import React from 'react';
import DistributionComponent from './Distribution';
import OrderComponent from './Order';
// import StatsComponent from './Stats';
import TypeComponent from './Type';

const RightsCardPage: React.FC = () => {
  const items = [
    {
      key: 'type',
      label: '权益卡维护',
      children: <TypeComponent />,
    },
    {
      key: 'distribution',
      label: '权益卡发放',
      children: <DistributionComponent />,
    },
    {
      key: 'order',
      label: '权益卡订单管理',
      children: <OrderComponent />,
    },
    // {
    //   key: 'stats',
    //   label: '权益卡使用统计',
    //   children: <StatsComponent />,
    // },
  ];

  return (
    <Tabs
      size="large"
      tabBarStyle={{ marginBottom: 24 }}
      items={items}
      destroyInactiveTabPane
    />
  );
};

export default RightsCardPage;
