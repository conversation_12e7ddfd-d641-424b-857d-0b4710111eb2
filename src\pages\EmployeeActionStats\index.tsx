import { PageContainer } from '@ant-design/pro-components';
import { Card, Tabs } from 'antd';
import React, { useState } from 'react';
import { ActionLogList, ActionTimeline } from './components';

const EmployeeActionStats: React.FC = () => {
  const [activeKey, setActiveKey] = useState('list');

  const tabItems = [
    {
      key: 'list',
      label: '动作记录',
      children: <ActionLogList />,
    },
    {
      key: 'timeline',
      label: '动作时间线',
      children: <ActionTimeline />,
    },
  ];

  return (
    <PageContainer
      title="员工操作统计"
      content="统计和分析员工在订单处理过程中的关键操作行为，包括接单、出发、开始服务、完成订单等动作记录"
    >
      <Card>
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>
    </PageContainer>
  );
};

export default EmployeeActionStats;
