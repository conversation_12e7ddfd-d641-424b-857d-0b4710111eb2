import { getAdditionalServices } from '@/services/additional-service-orders';
import { Descriptions, Modal, Spin, Table, Tabs, Tag, message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

// 追加服务状态工具函数
const AdditionalServiceStatusUtils = {
  getStatusColor: (status: string) => {
    switch (status) {
      case 'pending_confirm':
        return 'orange';
      case 'confirmed':
        return 'blue';
      case 'rejected':
        return 'red';
      case 'pending_payment':
        return 'gold';
      case 'paid':
        return 'processing';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'default';
      case 'refunding':
        return 'warning';
      case 'refunded':
        return 'error';
      default:
        return 'default';
    }
  },

  getStatusText: (status: string) => {
    switch (status) {
      case 'pending_confirm':
        return '待确认';
      case 'confirmed':
        return '已确认';
      case 'rejected':
        return '已拒绝';
      case 'pending_payment':
        return '待付款';
      case 'paid':
        return '已付款/服务中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      case 'refunding':
        return '退款中';
      case 'refunded':
        return '已退款';
      default:
        return status;
    }
  },
};

// 计时状态工具函数
const DurationStatusUtils = {
  getStatusColor: (status: string) => {
    switch (status) {
      case 'not_started':
        return 'default';
      case 'in_progress':
        return 'processing';
      case 'completed':
        return 'success';
      case 'not_required':
        return 'default';
      default:
        return 'default';
    }
  },

  getStatusText: (status: string) => {
    switch (status) {
      case 'not_started':
        return '未开始';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'not_required':
        return '无需计时';
      default:
        return status;
    }
  },
};

interface AdditionalServiceModalProps {
  visible: boolean;
  orderDetailId?: number;
  onClose: () => void;
}

const AdditionalServiceModal: React.FC<AdditionalServiceModalProps> = ({
  visible,
  orderDetailId,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [responseData, setResponseData] =
    useState<API.AdditionalServiceResponse | null>(null);

  const fetchAdditionalServices = async () => {
    if (!orderDetailId) return;

    setLoading(true);
    try {
      const { errCode, msg, data } = await getAdditionalServices(orderDetailId);
      if (errCode) {
        message.error(msg || '获取追加服务列表失败');
      } else {
        setResponseData(data || null);
      }
    } catch (error) {
      console.error('获取追加服务失败:', error);
      message.error('获取追加服务失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && orderDetailId) {
      fetchAdditionalServices();
    }
  }, [visible, orderDetailId]);

  // 原始增项服务表格列定义
  const originalServiceColumns: ColumnsType<API.AdditionalService> = [
    {
      title: '服务名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '服务类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `¥${price || 0}`,
    },
    {
      title: '时长',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => (duration ? `${duration}分钟` : '-'),
    },
    {
      title: '计时状态',
      dataIndex: 'durationStatus',
      key: 'durationStatus',
      render: (status: string) =>
        status ? (
          <Tag color={DurationStatusUtils.getStatusColor(status)}>
            {DurationStatusUtils.getStatusText(status)}
          </Tag>
        ) : (
          '-'
        ),
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
  ];

  // 追加服务订单表格列定义
  const additionalOrderColumns: ColumnsType<API.AdditionalServiceOrder> = [
    {
      title: '订单编号',
      dataIndex: 'sn',
      key: 'sn',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={AdditionalServiceStatusUtils.getStatusColor(status)}>
          {AdditionalServiceStatusUtils.getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '原价',
      dataIndex: 'originalPrice',
      key: 'originalPrice',
      width: 100,
      render: (price: number) => `¥${price || 0}`,
    },
    {
      title: '卡扣减',
      dataIndex: 'cardDeduction',
      key: 'cardDeduction',
      width: 100,
      render: (amount: number) => (amount ? `¥${amount}` : '-'),
    },
    {
      title: '券扣减',
      dataIndex: 'couponDeduction',
      key: 'couponDeduction',
      width: 100,
      render: (amount: number) => (amount ? `¥${amount}` : '-'),
    },
    {
      title: '实付金额',
      dataIndex: 'totalFee',
      key: 'totalFee',
      width: 100,
      render: (fee: number) => `¥${fee || 0}`,
    },
    {
      title: '确认时间',
      dataIndex: 'confirmTime',
      key: 'confirmTime',
      width: 160,
      render: (time: string) => (time ? new Date(time).toLocaleString() : '-'),
    },
  ];

  const expandedRowRender = (record: API.AdditionalServiceOrder) => {
    const detailColumns: ColumnsType<API.AdditionalServiceOrderDetail> = [
      {
        title: '序号',
        dataIndex: 'sequenceNumber',
        key: 'sequenceNumber',
        width: 60,
      },
      {
        title: '基础服务',
        dataIndex: 'serviceName',
        key: 'serviceName',
        render: (name: string, detail) => (
          <div>
            <div>{name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ¥{detail.servicePrice || 0}
            </div>
          </div>
        ),
      },
      {
        title: '增项服务',
        dataIndex: 'additionalServiceName',
        key: 'additionalServiceName',
        render: (name: string, detail) => (
          <div>
            <div>{name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ¥{detail.price || 0} | {detail.type}
            </div>
          </div>
        ),
      },
      {
        title: '计时状态',
        dataIndex: 'durationStatus',
        key: 'durationStatus',
        render: (status: string) =>
          status ? (
            <Tag color={DurationStatusUtils.getStatusColor(status)}>
              {DurationStatusUtils.getStatusText(status)}
            </Tag>
          ) : (
            '-'
          ),
      },
      {
        title: '说明',
        dataIndex: 'description',
        key: 'description',
        ellipsis: true,
      },
    ];

    return (
      <div style={{ margin: '16px 0' }}>
        <Descriptions
          title="客户信息"
          size="small"
          column={3}
          style={{ marginBottom: 16 }}
        >
          <Descriptions.Item label="客户姓名">
            {record.customer?.nickname || '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="联系电话">
            {record.customer?.phone || '未知'}
          </Descriptions.Item>
        </Descriptions>
        <Table
          columns={detailColumns}
          dataSource={record.details || []}
          pagination={false}
          size="small"
          rowKey="id"
          title={() =>
            `服务详情 (共${record.details?.[0]?.totalQuantity || 0}项)`
          }
        />
      </div>
    );
  };

  const renderSummary = () => {
    if (!responseData?.summary) return null;

    const { summary } = responseData;
    return (
      <div
        style={{
          marginBottom: 16,
          padding: 16,
          background: '#f5f5f5',
          borderRadius: 6,
        }}
      >
        <Descriptions title="汇总信息" size="small" column={3}>
          <Descriptions.Item label="原始增项服务">
            {summary.originalCount}项
          </Descriptions.Item>
          <Descriptions.Item label="追加订单">
            {summary.additionalOrdersCount}个
          </Descriptions.Item>
          <Descriptions.Item label="总计">
            {summary.totalCount}项
          </Descriptions.Item>
        </Descriptions>
      </div>
    );
  };

  const tabItems = [
    {
      key: 'original',
      label: `原始增项服务 (${
        responseData?.originalAdditionalServices?.length || 0
      })`,
      children: (
        <Table
          columns={originalServiceColumns}
          dataSource={responseData?.originalAdditionalServices || []}
          pagination={false}
          rowKey="id"
          size="small"
        />
      ),
    },
    {
      key: 'additional',
      label: `追加订单 (${responseData?.additionalServiceOrders?.length || 0})`,
      children: (
        <Table
          columns={additionalOrderColumns}
          dataSource={responseData?.additionalServiceOrders || []}
          pagination={false}
          rowKey="id"
          expandable={{
            expandedRowRender,
            defaultExpandAllRows: false,
          }}
          size="small"
        />
      ),
    },
  ];

  return (
    <Modal
      title="追加服务详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      destroyOnClose
    >
      <Spin spinning={loading}>
        {responseData ? (
          <>
            {renderSummary()}
            <Tabs items={tabItems} destroyInactiveTabPane />
          </>
        ) : (
          <div
            style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
          >
            暂无追加服务
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default AdditionalServiceModal;
