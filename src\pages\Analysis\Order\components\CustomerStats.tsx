import * as order from '@/services/order';
import { ReloadOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, Button, message, Space, Tag } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useRef } from 'react';

interface CustomerStatsProps {
  dateRange: [Dayjs, Dayjs];
}

interface CustomerData {
  customerId: number;
  customerName: string;
  customerPhone: string;
  customerAvatar?: string;
  memberStatus: number;
  orderCount: number;
  totalAmount: number;
  avgAmount: number;
}

const CustomerStats: React.FC<CustomerStatsProps> = ({ dateRange }) => {
  const actionRef = useRef<ActionType>();

  // 表格列定义
  const columns: ProColumns<CustomerData>[] = [
    {
      title: '客户ID',
      dataIndex: 'customerId',
      key: 'customerId',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '客户信息',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar
            src={record.customerAvatar}
            size="small"
            style={{ backgroundColor: '#52c41a' }}
          >
            {record.customerName?.charAt(0)}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500 }}>{record.customerName}</div>
            <div style={{ fontSize: '12px', color: '#999' }}>
              {record.customerPhone}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '会员状态',
      dataIndex: 'memberStatus',
      key: 'memberStatus',
      width: 100,
      align: 'center',
      valueEnum: {
        0: '普通会员',
        1: '权益卡会员',
      },
      render: (status) => (
        <Tag color={status === 1 ? 'gold' : 'default'}>
          {status === 1 ? '权益卡会员' : '普通会员'}
        </Tag>
      ),
    },
    {
      title: '订单数量',
      dataIndex: 'orderCount',
      key: 'orderCount',
      width: 100,
      align: 'center',
      hideInSearch: true,
      sorter: true,
      render: (count) => (
        <Tag color="blue" style={{ margin: 0 }}>
          {count} 单
        </Tag>
      ),
    },
    {
      title: '总消费',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      align: 'right',
      hideInSearch: true,
      sorter: true,
      render: (_amount, entity) => (
        <span style={{ fontWeight: 500, color: '#52c41a' }}>
          ¥{entity.totalAmount?.toFixed(2)}
        </span>
      ),
    },
    {
      title: '平均消费',
      dataIndex: 'avgAmount',
      key: 'avgAmount',
      width: 120,
      align: 'right',
      hideInSearch: true,
      sorter: true,
      render: (_amount, entity) => (
        <span style={{ color: '#666' }}>¥{entity.avgAmount?.toFixed(2)}</span>
      ),
    },
    // {
    //   title: '客户价值',
    //   key: 'customerValue',
    //   width: 100,
    //   align: 'center',
    //   hideInSearch: true,
    //   render: (_, record) => {
    //     let color = 'default';
    //     let text = '普通';

    //     if (record.totalAmount >= 1000) {
    //       color = 'red';
    //       text = '高价值';
    //     } else if (record.totalAmount >= 500) {
    //       color = 'orange';
    //       text = '中价值';
    //     } else if (record.totalAmount >= 200) {
    //       color = 'blue';
    //       text = '潜力';
    //     }

    //     return <Tag color={color}>{text}</Tag>;
    //   },
    // },
    // {
    //   title: '操作',
    //   key: 'action',
    //   width: 100,
    //   fixed: 'right',
    //   hideInSearch: true,
    //   render: (_, record) => (
    //     <Space>
    //       <Button
    //         type="link"
    //         size="small"
    //         icon={<EyeOutlined />}
    //         onClick={() => {
    //           // 这里可以添加查看客户详细统计的功能
    //           message.info(`查看客户 ${record.customerName} 的详细统计`);
    //         }}
    //       >
    //         详情
    //       </Button>
    //     </Space>
    //   ),
    // },
  ];

  return (
    <ProTable<CustomerData>
      actionRef={actionRef}
      rowKey="customerId"
      headerTitle="客户消费统计"
      columns={columns}
      search={false}
      pagination={{
        pageSize: 20,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
      }}
      scroll={{ x: 900 }}
      request={async (params, sort) => {
        try {
          const { errCode, msg, data } = await order.customerStats({
            ...params,
            startDate: dateRange[0].format('YYYY-MM-DD'),
            endDate: dateRange[1].format('YYYY-MM-DD'),
            sortBy: sort?.orderCount
              ? 'orderCount'
              : sort?.totalAmount
              ? 'totalAmount'
              : sort?.avgAmount
              ? 'avgAmount'
              : undefined,
            sortOrder: sort?.orderCount
              ? sort.orderCount === 'ascend'
                ? 'asc'
                : 'desc'
              : sort?.totalAmount
              ? sort.totalAmount === 'ascend'
                ? 'asc'
                : 'desc'
              : sort?.avgAmount
              ? sort.avgAmount === 'ascend'
                ? 'asc'
                : 'desc'
              : undefined,
          });

          if (errCode) {
            message.error(msg || '获取客户统计失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }

          return {
            data: data?.list || [],
            total: data?.total || 0,
            success: true,
          };
        } catch (error) {
          console.error('获取客户统计失败:', error);
          message.error('获取客户统计失败');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }
      }}
      toolBarRender={() => [
        <Button
          key="refresh"
          icon={<ReloadOutlined />}
          onClick={() => actionRef.current?.reload()}
        >
          刷新
        </Button>,
      ]}
    />
  );
};

export default CustomerStats;
