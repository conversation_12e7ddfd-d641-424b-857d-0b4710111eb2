import {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Col, Form, Row } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Vehicle;
  onSave: (info: API.Vehicle) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        form.setFieldsValue(info);
      } else {
        // 新增模式：重置表单
        form.resetFields();
        form.setFieldsValue({
          status: '空闲', // 默认状态
        });
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm<API.Vehicle>
      title={info ? '编辑车辆信息' : '注册车辆信息'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      width={800}
      labelCol={{ flex: '8em' }}
      onFinish={onSave}
      form={form}
      isKeyPressSubmit
    >
      <ProFormText name="id" label="ID" hidden />

      <Row style={{ width: '100%' }}>
        <Col span={12}>
          <ProFormText
            name="plateNumber"
            label="车牌号"
            colProps={{ span: 24 }}
            rules={[{ required: true, message: '请输入车牌号！' }]}
          />
          <ProFormDigit
            name="mileage"
            label="里程数"
            colProps={{ span: 24 }}
            fieldProps={{
              precision: 2,
              addonAfter: '公里',
            }}
            placeholder="请输入里程数"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="status"
            label="车辆状态"
            colProps={{ span: 24 }}
            rules={[
              { required: true, message: '请输入车辆状态！' },
              { max: 6, message: '状态长度不能超过6个字符！' },
            ]}
            fieldProps={{
              maxLength: 6,
            }}
          />
          <div
            style={{
              gridColumn: 'span 4',
              display: 'flex',
              alignItems: 'center',
              paddingLeft: '8em',
              gap: '8px',
            }}
          >
            <Button
              size="small"
              onClick={() => form.setFieldValue('status', '空闲')}
            >
              空闲
            </Button>
            <Button
              size="small"
              onClick={() => form.setFieldValue('status', '忙碌')}
            >
              忙碌
            </Button>
            <Button
              size="small"
              onClick={() => form.setFieldValue('status', '保养中')}
            >
              保养中
            </Button>
          </div>
        </Col>
      </Row>

      {/* 扩展信息 */}

      <ProFormTextArea
        name="appearance"
        label="外观描述"
        colProps={{ span: 24 }}
        placeholder="请描述车辆外观状况"
        fieldProps={{
          rows: 3,
        }}
      />
      <ProFormDatePicker
        name="insuranceExpiry"
        label="保险到期时间"
        colProps={{ span: 12 }}
        placeholder="请选择保险到期时间"
      />
      <ProFormDatePicker
        name="licenseExpiry"
        label="行驶证到期时间"
        colProps={{ span: 12 }}
        placeholder="请选择行驶证到期时间"
      />
      <ProFormTextArea
        name="supplies"
        label="物资清单"
        colProps={{ span: 24 }}
        placeholder="请输入车辆物资清单"
        fieldProps={{
          rows: 3,
        }}
      />
    </ModalForm>
  );
};

export default EditModal;
