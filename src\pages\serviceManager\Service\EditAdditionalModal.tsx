import { DictionarieState } from '@/models/dictionarie';
import { index } from '@/services/additional-service';
import { connect } from '@umijs/max';
import { Drawer, Table, TableColumnsType } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import React, { useEffect, useState } from 'react';

type EditModalProps = {
  open: boolean;
  service?: API.Service;
  list?: number[];
  onSave: (list: number[]) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  service,
  list,
  onSave,
  onClose,
  dictionarie,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [baseList, setBaseList] = useState<API.AdditionalServiceAttrs[]>([]);

  useEffect(() => {
    if (list) {
      setSelectedRowKeys(list);
    }
  }, [list]);

  const columns: TableColumnsType<API.AdditionalServiceAttrs> = [
    { title: 'id', dataIndex: 'id', hidden: true },
    { title: '名称', dataIndex: 'name' },
    {
      title: '类别',
      dataIndex: 'type',
      render: (_type, record) => {
        return (
          dictionarie.list
            .filter((d) => d.type === '增项服务类型')
            .find((d) => d.code === record.type)?.name || ''
        );
      },
    },
    {
      title: '价格',
      dataIndex: 'price',
      render: (price) => `¥${price}`,
    },
    {
      title: '统计时长',
      dataIndex: 'needDurationTracking',
      render: (needDurationTracking) => (
        <span style={{ color: needDurationTracking ? '#52c41a' : '#d9d9d9' }}>
          {needDurationTracking ? '是' : '否'}
        </span>
      ),
    },
  ];

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
    const list = newSelectedRowKeys.map((key) => Number(key));
    onSave(list);
  };

  const rowSelection: TableRowSelection<API.AdditionalServiceAttrs> = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  useEffect(() => {
    (async () => {
      const res = await index({});
      setBaseList(res.data?.list || []);
    })();
  }, []);

  return (
    <Drawer
      title={`增项服务配置-${service?.serviceName || '未选择'}`}
      open={open}
      onClose={onClose}
    >
      <Table<API.AdditionalServiceAttrs>
        rowKey="id"
        rowSelection={rowSelection}
        columns={columns}
        dataSource={baseList}
        pagination={false}
      />
    </Drawer>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(EditModal);
