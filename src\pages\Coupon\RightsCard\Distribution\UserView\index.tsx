import { getCustomers } from '@/services/customers';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Drawer, message, Space, Tag, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import BatchIssueCardModal from '../components/BatchIssueCardModal';
import CardList from './CardList';

/**
 * 用户视角组件
 */
const UserView: React.FC = () => {
  // 表格操作引用
  const actionRef = useRef<ActionType>();

  // 抽屉状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [currentUserId, setCurrentUserId] = useState<number>(0);
  const [currentUserName, setCurrentUserName] = useState<string>('');

  // 批量发放状态
  const [batchIssueModalVisible, setBatchIssueModalVisible] =
    useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<API.Customer[]>([]);

  // 处理查看权益卡列表
  const handleViewCards = (record: API.Customer) => {
    setCurrentUserId(record.id);
    setCurrentUserName(record.nickname || `用户${record.id}`);
    setDrawerVisible(true);
  };

  // 处理行选择变化
  const handleRowSelectionChange = (
    selectedRowKeys: React.Key[],
    selectedRows: API.Customer[],
  ) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedUsers(selectedRows);
  };

  // 处理批量发放
  const handleBatchIssue = () => {
    if (selectedUsers.length === 0) {
      message.warning('请先选择要发放权益卡的用户');
      return;
    }
    setBatchIssueModalVisible(true);
  };

  // 表格列定义
  const columns: ProColumns<API.Customer>[] = [
    {
      title: '用户ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'nickname',
      ellipsis: true,
      width: 120,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      hideInSearch: true,
      width: 180,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '会员状态',
      dataIndex: 'memberStatus',
      width: 100,
      valueEnum: {
        0: { text: '普通会员', status: 'Default' },
        1: { text: '权益卡会员', status: 'Success' },
      },
    },
    {
      title: '权益卡',
      dataIndex: 'membershipCards',
      hideInSearch: true,
      width: 200,
      render: (_, record) => {
        // 这里假设API返回了membershipCards字段，如果没有，需要单独获取
        const cards = record.membershipCards || [];
        if (cards.length === 0) {
          return '无';
        }

        return (
          <Tooltip title={cards.map((card) => card.cardType?.name).join(', ')}>
            <div>
              {cards.slice(0, 2).map((card) => (
                <Tag key={card.id} color="blue">
                  {card.cardType?.name}
                </Tag>
              ))}
              {cards.length > 2 && <Tag color="blue">+{cards.length - 2}</Tag>}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '积分',
      dataIndex: 'points',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        1: { text: '正常', status: 'Success' },
        0: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => handleViewCards(record)}>
            管理
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Customer>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
        rowSelection={{
          selectedRowKeys,
          onChange: handleRowSelectionChange,
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
          <span>
            已选择 <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a>{' '}
            个用户
            <a style={{ marginLeft: 24 }} onClick={onCleanSelected}>
              取消选择
            </a>
          </span>
        )}
        tableAlertOptionRender={() => {
          return (
            <Space size={16}>
              <Button type="primary" onClick={handleBatchIssue}>
                批量发放权益卡
              </Button>
            </Space>
          );
        }}
        request={async (params) => {
          const response = await getCustomers({
            ...params,
          });

          if (response.errCode) {
            message.error(response.msg || '获取用户列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />

      {/* 权益卡列表抽屉 */}
      <Drawer
        title={`${currentUserName} - 权益卡列表`}
        width={1200}
        placement="right"
        onClose={() => {
          setCurrentUserId(0);
          setCurrentUserName('');
          setDrawerVisible(false);
        }}
        open={drawerVisible}
        destroyOnClose={true}
      >
        <CardList userId={currentUserId} />
      </Drawer>

      {/* 批量发放权益卡模态框 */}
      <BatchIssueCardModal
        open={batchIssueModalVisible}
        onClose={() => setBatchIssueModalVisible(false)}
        onSuccess={() => {
          setBatchIssueModalVisible(false);
          setSelectedRowKeys([]);
          setSelectedUsers([]);
          actionRef.current?.reload();
        }}
        selectedUserIds={selectedUsers.map((user) => user.id)}
        selectedUsers={selectedUsers}
      />
    </>
  );
};

export default UserView;
