/*
 * @Description: 计算类工具
 * @Date: 2025-01-23 12:16:12
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 09:50:52
 */

/**
 * 将列表转换为树形结构
 *
 * @param {Object} params 参数
 * @param {Array} param.list 列表
 * @param {String} [param.parentKey] 父级key，不传递为根节点
 * @param {String} [param.childKey] 返回数据的子级key，默认为children
 * @param {{parent: string; child: string}} [param.fieldName] 定义父子关系的字段名称，默认为{parent: 'code', child: 'parentCode'}
 * @returns {Object} res
 */
export const convertListToTree = ({
  list,
  parentKey,
  childKey = 'children',
  fieldName = {
    parent: 'code',
    child: 'parentCode',
  },
}: {
  list: any[];
  parentKey?: string;
  childKey?: string;
  fieldName?: {
    parent: string;
    child: string;
  };
}) => {
  const tree: any[] = [];
  const map = new Map();
  list.forEach((item) => {
    map.set(item[fieldName.parent], { ...item, [childKey]: [] });
  });
  list.forEach((item) => {
    const parent = map.get(item[fieldName.child]);
    if (parent) {
      parent[childKey].push(map.get(item[fieldName.parent]));
    } else if (!parentKey || !item[fieldName.child]) {
      tree.push(map.get(item[fieldName.parent]));
    }
  });
  // 删除children长度为0的节点
  const removeEmptyChildren = (node: any) => {
    if (!node) {
      console.warn('node is null');
      return;
    }
    if (node[childKey] && node[childKey].length === 0) {
      delete node[childKey];
    } else if (node[childKey]) {
      node[childKey].forEach((child: any) => removeEmptyChildren(child));
    }
  };
  tree.forEach((node) => removeEmptyChildren(node));
  return tree;
};

export type OnChangePayload<T> = {
  filters: Record<string, (React.Key | boolean)[] | null>;
  sorter: {
    field: keyof T;
    order: 'descend' | 'ascend' | null;
  };
};

/**
 * proTable组件的过滤和排序操作，分页不用处理，组件自己会完成
 *
 * @template T 列表数据类型
 * @param {T[]} list 列表数据
 * @param {OnChangePayload<T>} payload 过滤和排序操作的参数
 * @return {T[]}
 */
export const onProTableChange = <T = Record<string, any>>(
  list: T[],
  payload: OnChangePayload<T>,
): T[] => {
  const { filters, sorter } = payload;
  const { field, order } = sorter;

  // 实现过滤，字符串类型支持模糊查询
  let filteredList = [...list];
  if (Object.keys(filters).length > 0) {
    filteredList = list.filter((item) => {
      return Object.keys(filters).every((key) => {
        const filterValues = filters[key];
        const value = item[key as keyof T];
        if (
          filterValues &&
          Array.isArray(filterValues) &&
          ['string', 'number', 'boolean'].includes(typeof value)
        ) {
          if (typeof value === 'string') {
            // 字符串类型支持模糊查询
            return filterValues.some((filterValue) =>
              value.includes(filterValue as string),
            );
          } else {
            return filterValues.includes(value as unknown as string);
          }
        }
        return true;
      });
    });
  }

  // 实现排序
  if (field && order) {
    filteredList = filteredList.sort((a, b) => {
      const aValue = a[field];
      const bValue = b[field];
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        if (order === 'ascend') {
          return aValue - bValue;
        } else if (order === 'descend') {
          return bValue - aValue;
        }
      } else if (typeof aValue === 'string' && typeof bValue === 'string') {
        if (order === 'ascend') {
          return aValue.localeCompare(bValue);
        } else if (order === 'descend') {
          return bValue.localeCompare(aValue);
        }
      } else if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
        const av = aValue ? 1 : 0;
        const bv = bValue ? 1 : 0;
        if (order === 'ascend') {
          return av - bv;
        } else if (order === 'descend') {
          return bv - av;
        }
      }
      return 0;
    });
  }

  return filteredList;
};

/** proTable组件的查询操作 */
/**
 * proTable组件的查询操作
 *
 * @template T - 列表数据类型
 * @param {T[]} list - 列表数据
 * @param {Partial<T>} payload - 查询参数
 * @return {T[]}
 */
export const onProTableSearch = <T = Record<string, any>>(
  list: T[],
  payload: Partial<T>,
): T[] => {
  const newList = list.filter((item: T) => {
    return Object.keys(payload).every((key) => {
      // 判断key在数据结构中是否存在
      if (Object.prototype.hasOwnProperty.call(item, key)) {
        return String(item[key as keyof T])
          .toLowerCase()
          .includes(String(payload[key as keyof T]).toLowerCase());
      }
      return true;
    });
  });
  return newList;
};

/**
 * 中文数字与阿拉伯数字相互转换
 *
 * @param {string | number} input 输入的数字，可以是中文或阿拉伯数字
 * @returns {string | number} 转换后的数字
 */
export const convertNumber = (input: string | number): string | number => {
  const chineseToArabic = (chinese: string): number => {
    const chineseNumbers: { [key: string]: number } = {
      零: 0,
      一: 1,
      二: 2,
      三: 3,
      四: 4,
      五: 5,
      六: 6,
      七: 7,
      八: 8,
      九: 9,
    };
    const units: { [key: string]: number } = {
      十: 10,
      百: 100,
      千: 1000,
      万: 10000,
      亿: 100000000,
    };

    let result = 0;
    let unit = 1;
    let temp = 0;

    for (let i = chinese.length - 1; i >= 0; i--) {
      const char = chinese[i];
      if (units[char]) {
        unit = units[char];
        if (unit === 10 && (i === 0 || chinese[i - 1] in units)) {
          temp += unit;
        }
      } else {
        const num = chineseNumbers[char];
        if (num !== undefined) {
          temp += num * unit;
        }
      }
    }
    result += temp;
    return result;
  };

  const arabicToChinese = (arabic: number): string => {
    const chineseNumbers = [
      '零',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
    ];
    const units = ['', '十', '百', '千', '万', '亿'];

    if (arabic === 0) return chineseNumbers[0];

    let result = '';
    let unitIndex = 0;
    let remaining = arabic;

    while (remaining > 0) {
      const digit = remaining % 10;
      if (digit > 0) {
        result = chineseNumbers[digit] + units[unitIndex] + result;
      } else if (result && result[0] !== chineseNumbers[0]) {
        result = chineseNumbers[0] + result;
      }
      remaining = Math.floor(remaining / 10);
      unitIndex++;
    }

    return result.replace(/^一十/, '十');
  };

  if (typeof input === 'string') {
    return chineseToArabic(input);
  } else if (typeof input === 'number') {
    return arabicToChinese(input);
  }
  return input;
};

// 根据出生年月计算年龄
export const calculateAge = (birthDate: Date | string) => {
  if (!birthDate) {
    return '';
  }
  const today = new Date();
  const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate; // ty
  let age = today.getFullYear() - birth.getFullYear();
  const m = today.getMonth() - birth.getMonth(); // 月
  if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  return age;
};

/**
 * 从员工职位信息中获取车辆类型名称
 * @param {API.Dictionarie[]} dictionarys 字典缓存
 * @param {string} position 员工职位名称
 * @returns {String} 车辆类型名称
 */
export const getVehicleTypeNameFromEmpolyee = (
  dictionarys: API.Dictionarie[],
  position?: string,
) => {
  if (!position) {
    return '';
  }
  const positionObj = dictionarys
    .filter((d) => d.type === '员工职位' && !!d.status)
    .find((d) => d.code === position);
  if (!positionObj) {
    return '';
  }
  const positionName = positionObj.name;
  return positionName.replace('师', '');
};
