import { CouponOrderStatus } from '@/constant';
import { Descriptions, Drawer, Space, Tag, Typography } from 'antd';
import moment from 'moment';
import React from 'react';

const { Text } = Typography;

interface OrderDetailDrawerProps {
  visible: boolean;
  order?: API.CouponOrder;
  onClose: () => void;
}

/**
 * 代金券订单详情抽屉组件
 */
const OrderDetailDrawer: React.FC<OrderDetailDrawerProps> = ({
  visible,
  order,
  onClose,
}) => {
  // 渲染订单状态标签
  const renderStatusTag = (status?: CouponOrderStatus) => {
    if (!status) return <Tag>未知</Tag>;

    switch (status) {
      case CouponOrderStatus.待付款:
        return <Tag color="warning">待付款</Tag>;
      case CouponOrderStatus.已付款:
        return <Tag color="success">已付款</Tag>;
      case CouponOrderStatus.已取消:
        return <Tag>已取消</Tag>;
      case CouponOrderStatus.已退款:
        return <Tag color="error">已退款</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  return (
    <Drawer
      title="代金券订单详情"
      width={600}
      open={visible}
      onClose={onClose}
      destroyOnClose
    >
      {order ? (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Descriptions title="基本信息" column={1} bordered>
            <Descriptions.Item label="订单ID">{order.id}</Descriptions.Item>
            <Descriptions.Item label="订单编号">{order.sn}</Descriptions.Item>
            <Descriptions.Item label="订单状态">
              {renderStatusTag(order.status)}
            </Descriptions.Item>
            <Descriptions.Item label="订单金额">
              <Text type="danger">¥{order.amount}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {order.createdAt
                ? moment(order.createdAt).format('YYYY-MM-DD HH:mm:ss')
                : '-'}
            </Descriptions.Item>
            {order.payTime && (
              <Descriptions.Item label="支付时间">
                {moment(order.payTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {order.cancelTime && (
              <Descriptions.Item label="取消时间">
                {moment(order.cancelTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {order.refundTime && (
              <Descriptions.Item label="退款时间">
                {moment(order.refundTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {order.prepayId && (
              <Descriptions.Item label="微信支付预支付ID">
                {order.prepayId}
              </Descriptions.Item>
            )}
            {order.remark && (
              <Descriptions.Item label="备注">{order.remark}</Descriptions.Item>
            )}
          </Descriptions>

          <Descriptions title="用户信息" column={1} bordered>
            <Descriptions.Item label="用户ID">
              {order.customerId}
            </Descriptions.Item>
            <Descriptions.Item label="用户昵称">
              {order.customer?.nickname || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="手机号码">
              {order.customer?.phone || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="用户头像">
              {order.customer?.avatar ? (
                <img
                  src={order.customer.avatar}
                  alt="用户头像"
                  style={{ width: 40, height: 40, borderRadius: '50%' }}
                />
              ) : (
                '-'
              )}
            </Descriptions.Item>
          </Descriptions>

          <Descriptions title="代金券信息" column={1} bordered>
            <Descriptions.Item label="代金券ID">
              {order.couponId}
            </Descriptions.Item>
            <Descriptions.Item label="代金券名称">
              {order.coupon?.name || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="面额">
              ¥{order.coupon?.amount || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="销售价格">
              ¥{order.coupon?.price || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="最低消费金额">
              {order.coupon?.minAmount
                ? `¥${order.coupon.minAmount}`
                : '无限制'}
            </Descriptions.Item>
            <Descriptions.Item label="有效期">
              {order.coupon?.validFrom && order.coupon?.validTo
                ? `${moment(order.coupon.validFrom).format(
                    'YYYY-MM-DD',
                  )} 至 ${moment(order.coupon.validTo).format('YYYY-MM-DD')}`
                : '无限制'}
            </Descriptions.Item>
            <Descriptions.Item label="代金券描述">
              {order.coupon?.description || '-'}
            </Descriptions.Item>
          </Descriptions>
        </Space>
      ) : (
        <div>暂无订单信息</div>
      )}
    </Drawer>
  );
};

export default OrderDetailDrawer;
