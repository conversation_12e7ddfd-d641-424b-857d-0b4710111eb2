import { DictionarieState } from '@/models/dictionarie';
import { getVehicleTypeNameFromEmpolyee } from '@/utils/calc';
import { connect } from '@umijs/max';
import { Descriptions, Modal, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

const { Text } = Typography;

interface DetailModalProps {
  /** 是否显示 */
  open: boolean;
  /** 车辆信息 */
  vehicle?: API.Vehicle;
  /** 关闭回调 */
  onClose: () => void;
  dictionarie: DictionarieState;
}

const DetailModal: React.FC<DetailModalProps> = ({
  open,
  vehicle,
  onClose,
  dictionarie,
}) => {
  if (!vehicle) return null;

  /** 格式化日期 */
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '-';
    return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
  };

  /** 格式化日期（仅日期） */
  const formatDateOnly = (dateStr?: string) => {
    if (!dateStr) return '-';
    return dayjs(dateStr).format('YYYY-MM-DD');
  };

  /** 检查是否即将到期 */
  const isExpiringSoon = (dateStr?: string, days = 30) => {
    if (!dateStr) return false;
    const expiry = dayjs(dateStr);
    const now = dayjs();
    return expiry.diff(now, 'day') <= days && expiry.isAfter(now);
  };

  /** 检查是否已过期 */
  const isExpired = (dateStr?: string) => {
    if (!dateStr) return false;
    return dayjs(dateStr).isBefore(dayjs());
  };

  /** 渲染到期状态 */
  const renderExpiryStatus = (dateStr?: string) => {
    if (!dateStr) return '-';

    const date = formatDateOnly(dateStr);
    if (isExpired(dateStr)) {
      return <Text type="danger">{date} (已过期)</Text>;
    } else if (isExpiringSoon(dateStr)) {
      return <Text type="warning">{date} (即将到期)</Text>;
    } else {
      return <Text type="success">{date}</Text>;
    }
  };

  const basicItems = [
    {
      key: 'plateNumber',
      label: '车牌号',
      children: vehicle.plateNumber,
    },
    {
      key: 'vehicleType',
      label: '车辆类型',
      children:
        getVehicleTypeNameFromEmpolyee(
          dictionarie?.list || [],
          vehicle.employee?.position,
        ) || '-',
    },
    {
      key: 'status',
      label: '车辆状态',
      children: (
        <Tag color={vehicle.status === '空闲' ? 'green' : 'blue'}>
          {vehicle.status}
        </Tag>
      ),
    },
    {
      key: 'employee',
      label: '当前员工',
      children: vehicle.employee ? (
        <div>
          <div>{vehicle.employee.name}</div>
          <Text type="secondary">{vehicle.employee.phone}</Text>
        </div>
      ) : (
        '-'
      ),
    },
    {
      key: 'location',
      label: '当前位置',
      children:
        vehicle.latitude && vehicle.longitude ? (
          <div>
            <div>纬度: {vehicle.latitude}</div>
            <div>经度: {vehicle.longitude}</div>
          </div>
        ) : (
          '-'
        ),
    },
  ];

  const extendedItems = [
    {
      key: 'mileage',
      label: '里程数',
      children: vehicle.mileage ? `${vehicle.mileage} 公里` : '-',
    },
    {
      key: 'appearance',
      label: '外观描述',
      children: vehicle.appearance || '-',
      span: 3,
    },
    {
      key: 'insuranceExpiry',
      label: '保险到期时间',
      children: renderExpiryStatus(vehicle.insuranceExpiry),
    },
    {
      key: 'licenseExpiry',
      label: '行驶证到期时间',
      children: renderExpiryStatus(vehicle.licenseExpiry),
    },
    {
      key: 'supplies',
      label: '物资清单',
      children: vehicle.supplies || '-',
      span: 3,
    },
  ];

  const submitItems = [
    {
      key: 'lastSubmittedAt',
      label: '最后提交时间',
      children: formatDate(vehicle.lastSubmittedAt),
    },
    {
      key: 'lastSubmittedEmployee',
      label: '最后提交人',
      children: vehicle.lastSubmittedEmployee ? (
        <div>
          <div>{vehicle.lastSubmittedEmployee.name}</div>
          <Text type="secondary">{vehicle.lastSubmittedEmployee.phone}</Text>
        </div>
      ) : (
        '-'
      ),
    },
    {
      key: 'createdAt',
      label: '创建时间',
      children: formatDate(vehicle.createdAt),
    },
    {
      key: 'updatedAt',
      label: '更新时间',
      children: formatDate(vehicle.updatedAt),
    },
  ];

  return (
    <Modal
      title="车辆详情"
      open={open}
      onCancel={onClose}
      footer={null}
      width={900}
      destroyOnClose
    >
      <Descriptions
        title="基本信息"
        bordered
        column={2}
        size="small"
        items={basicItems}
        style={{ marginBottom: 24 }}
      />

      <Descriptions
        title="扩展信息"
        bordered
        column={3}
        size="small"
        items={extendedItems}
        style={{ marginBottom: 24 }}
      />

      <Descriptions
        title="提交记录"
        bordered
        column={2}
        size="small"
        items={submitItems}
      />
    </Modal>
  );
};

export default connect(({ dictionarie }: { dictionarie: any }) => ({
  dictionarie,
}))(DetailModal);
