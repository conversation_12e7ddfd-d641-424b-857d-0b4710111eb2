import { getCouponsByUser } from '@/services/customer-coupons';
import { getCardsByUser } from '@/services/customer-membership-cards';
import type { ActionType } from '@ant-design/pro-components';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Modal, Tabs } from 'antd';
import moment from 'moment';
import React, { useRef } from 'react';

const { TabPane } = Tabs;

interface CouponListModalProps {
  open: boolean;
  onClose: () => void;
  customerId: number;
  customerName: string;
}

const CouponListModal: React.FC<CouponListModalProps> = ({
  open,
  onClose,
  customerId,
  customerName,
}) => {
  const rightsCardActionRef = useRef<ActionType>();
  const couponActionRef = useRef<ActionType>();

  // 权益卡列表列配置
  const rightsCardColumns: ProColumns<API.CustomerMembershipCard>[] = [
    {
      title: '权益卡名称',
      dataIndex: ['cardType', 'name'],
      width: 150,
      ellipsis: true,
    },
    {
      title: '卡类型',
      dataIndex: ['cardType', 'type'],
      width: 100,
      valueEnum: {
        discount: { text: '折扣卡', status: 'Processing' },
        times: { text: '次卡', status: 'Success' },
      },
    },
    {
      title: '剩余次数',
      dataIndex: 'remainTimes',
      width: 100,
      render: (_, record) =>
        record.remainTimes !== undefined
          ? record.remainTimes === -1
            ? '不限'
            : record.remainTimes
          : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      valueEnum: {
        active: { text: '有效', status: 'Success' },
        expired: { text: '已失效', status: 'Error' },
        used: { text: '已用完', status: 'Warning' },
      },
    },
    {
      title: '到期时间',
      dataIndex: 'expiryTime',
      width: 140,
      render: (_, record) =>
        record.expiryTime
          ? moment(record.expiryTime).format('YYYY-MM-DD HH:mm')
          : '无限期',
    },
    {
      title: '购买时间',
      dataIndex: 'purchaseTime',
      width: 140,
      render: (_, record) =>
        record.purchaseTime
          ? moment(record.purchaseTime).format('YYYY-MM-DD HH:mm')
          : '-',
    },
  ];

  // 代金券列表列配置
  const couponColumns: ProColumns<API.CustomerCoupon>[] = [
    {
      title: '面值',
      dataIndex: ['coupon', 'amount'],
      width: 100,
      valueType: 'money',
    },
    {
      title: '使用门槛',
      dataIndex: ['coupon', 'threshold'],
      width: 120,
      valueType: 'money',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      valueEnum: {
        unused: { text: '未使用', status: 'Success' },
        used: { text: '已使用', status: 'Default' },
        expired: { text: '已失效', status: 'Error' },
      },
    },
    {
      title: '领取时间',
      dataIndex: 'receiveTime',
      width: 140,
      render: (_, record) =>
        record.receiveTime
          ? moment(record.receiveTime).format('YYYY-MM-DD HH:mm')
          : '-',
    },
    {
      title: '到期时间',
      dataIndex: 'expiryTime',
      width: 140,
      render: (_, record) =>
        record.expiryTime
          ? moment(record.expiryTime).format('YYYY-MM-DD HH:mm')
          : '无限期',
    },
  ];

  return (
    <Modal
      title={`${customerName} - 卡券列表`}
      open={open}
      onCancel={onClose}
      footer={null}
      width={1200}
      destroyOnClose
    >
      <Tabs defaultActiveKey="rightsCards" destroyInactiveTabPane>
        <TabPane tab="权益卡" key="rightsCards">
          <ProTable<API.CustomerMembershipCard>
            actionRef={rightsCardActionRef}
            rowKey="id"
            columns={rightsCardColumns}
            search={false}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
            }}
            options={false}
            request={async (params) => {
              if (!customerId) {
                return {
                  data: [],
                  success: true,
                  total: 0,
                };
              }

              const response = await getCardsByUser(customerId, params);

              if (response.errCode) {
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }

              return {
                data: response.data?.list || [],
                success: true,
                total: response.data?.total || 0,
              };
            }}
          />
        </TabPane>
        <TabPane tab="代金券" key="coupons">
          <ProTable<API.CustomerCoupon>
            actionRef={couponActionRef}
            rowKey="id"
            columns={couponColumns}
            search={false}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
            }}
            options={false}
            request={async (params) => {
              if (!customerId) {
                return {
                  data: [],
                  success: true,
                  total: 0,
                };
              }

              const response = await getCouponsByUser(customerId, params);

              if (response.errCode) {
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }

              return {
                data: response.data?.list || [],
                success: true,
                total: response.data?.total || 0,
              };
            }}
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default CouponListModal;
