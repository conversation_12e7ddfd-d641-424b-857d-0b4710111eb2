import ProFormImg from '@/components/ProFormItem/ProFormImg';
import {
  ModalForm,
  ProFormDigit,
  ProFormRadio,
  ProFormSegmented,
  ProFormText,
} from '@ant-design/pro-components';
import { Col, Row } from 'antd';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Customer;
  onSave: (info: API.Customer) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  return (
    <ModalForm<API.Customer>
      title={info ? '编辑客户信息' : '注册客户信息'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      initialValues={info || { memberStatus: '0', status: '1' }}
    >
      <ProFormText name="id" label="ID" hidden />
      <Row style={{ width: '100%' }}>
        <Col span={14}>
          <ProFormText
            name="nickname"
            label="昵称"
            rules={[{ required: true, message: '请输入昵称！' }]}
          />
          <ProFormText
            name="phone"
            label="手机号"
            rules={[
              { required: true, message: '请输入手机号！' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号！' },
            ]}
          />
          <ProFormRadio.Group
            name="gender"
            label="性别"
            options={[
              { label: '保密', value: '2' },
              { label: '男', value: '1' },
              { label: '女', value: '0' },
            ]}
            convertValue={(value) => {
              return String(value);
            }}
          />
        </Col>
        <Col span={10}>
          <ProFormImg name="avatar" label="头像" />
        </Col>
      </Row>
      <ProFormText name="address" label="地址" />
      <ProFormSegmented
        name="memberStatus"
        label="会员状态"
        valueEnum={{
          0: '普通会员',
          1: '权益卡会员',
        }}
        convertValue={(value) => {
          return String(value);
        }}
        colProps={{ span: 12 }}
      />
      <ProFormDigit name="points" label="积分" colProps={{ span: 12 }} />
      <ProFormSegmented
        name="status"
        label="启用状态"
        fieldProps={{
          options: [
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' },
          ],
        }}
        convertValue={(value) => {
          return String(value);
        }}
        colProps={{ span: 12 }}
      />
    </ModalForm>
  );
};

export default EditModal;
