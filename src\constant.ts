export enum OrderStatus {
  待付款 = '待付款',
  待接单 = '待接单',
  待服务 = '待服务',
  已出发 = '已出发',
  服务中 = '服务中',
  已完成 = '已完成',
  已评价 = '已评价',
  已取消 = '已取消',
  退款中 = '退款中',
  已退款 = '已退款',
}

export const Gender: Record<string, string> = {
  '0': '未知',
  '1': '男',
  '2': '女',
};

export enum ApplicableScope {
  不限 = 'all',
  所有服务 = 'allServices',
  指定服务类别 = 'serviceType',
  指定服务品牌 = 'serviceCategory',
  指定服务 = 'service',
  所有商品 = 'allProducts',
  指定商品类别 = 'productCategory',
  指定商品 = 'product',
}

export enum MembershipCardOrderStatus {
  待付款 = 'pending_payment',
  已付款 = 'paid',
  已取消 = 'cancelled',
  已退款 = 'refunded',
}

export enum CouponOrderStatus {
  待付款 = 'pending_payment',
  已付款 = 'paid',
  已取消 = 'cancelled',
  已退款 = 'refunded',
}
