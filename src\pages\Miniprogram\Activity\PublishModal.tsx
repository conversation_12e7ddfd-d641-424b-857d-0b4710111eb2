import { getCurrentPublished } from '@/services/activity';
import { ModalForm, ProFormRadio } from '@ant-design/pro-components';
import { Alert, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Text } = Typography;

type PublishModalProps = {
  open: boolean;
  activity?: API.Activity;
  onPublish: (id: number) => Promise<void>;
  onUnpublish: (id: number) => Promise<void>;
  onClose: () => void;
};

const PublishModal: React.FC<PublishModalProps> = ({
  open,
  activity,
  onPublish,
  onUnpublish,
  onClose,
}) => {
  const [currentPublished, setCurrentPublished] = useState<API.Activity | null>(
    null,
  );
  const [loading, setLoading] = useState(false);

  const fetchCurrentPublished = async () => {
    if (!activity) return;

    setLoading(true);
    try {
      const { errCode, data } = await getCurrentPublished(activity.target);
      if (!errCode && data) {
        setCurrentPublished(data);
      } else {
        setCurrentPublished(null);
      }
    } catch (error) {
      setCurrentPublished(null);
    } finally {
      setLoading(false);
    }
  };

  const handleFinish = async (values: { action: string }) => {
    if (!activity) return;

    if (values.action === 'publish') {
      await onPublish(activity.id);
    } else {
      await onUnpublish(activity.id);
    }
  };

  useEffect(() => {
    if (open && activity) {
      fetchCurrentPublished();
    }
  }, [open, activity]);

  if (!activity) return null;

  const isCurrentlyPublished = activity.isPublished === 1;
  const hasOtherPublished =
    currentPublished && currentPublished.id !== activity.id;

  return (
    <ModalForm
      title="发布管理"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        width: 500,
      }}
      open={open}
      layout="horizontal"
      labelCol={{ flex: '6em' }}
      onFinish={handleFinish}
      loading={loading}
    >
      <div style={{ marginBottom: 16 }}>
        <Text strong>活动：</Text>
        <Text>{activity.title}</Text>
      </div>

      <div style={{ marginBottom: 16 }}>
        <Text strong>当前状态：</Text>
        <Text type={isCurrentlyPublished ? 'success' : 'secondary'}>
          {isCurrentlyPublished ? '已发布' : '未发布'}
        </Text>
      </div>

      {hasOtherPublished && (
        <Alert
          message="注意"
          description={`当前已有活动"${currentPublished.title}"正在发布中，发布新活动将自动取消该活动的发布状态。`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <ProFormRadio.Group
        name="action"
        label="操作"
        options={[
          ...(isCurrentlyPublished
            ? [{ label: '取消发布', value: 'unpublish' }]
            : [{ label: '发布活动', value: 'publish' }]),
        ]}
        rules={[{ required: true, message: '请选择操作！' }]}
      />
      <Alert
        message="注意：同一时间只能发布一个活动，发布新活动将自动取消其他活动的发布状态。"
        type="info"
        // showIcon
      />
    </ModalForm>
  );
};

export default PublishModal;
